import PyInstaller.__main__
import os
import shutil

# Nettoyer les builds précédents
if os.path.exists('build'):
    shutil.rmtree('build')
if os.path.exists('dist'):
    shutil.rmtree('dist')

# Configuration de PyInstaller
PyInstaller.__main__.run([
    'launcher v2.py',
    '--name=Cracken Launcher',
    '--onefile',
    '--windowed',
    '--icon=exe_icon_multi_hd.ico',  # Utilise l'icône HD multi-résolution
    '--add-data=crackenlauncher_logo.png;.',
    '--add-data=exe_icon_multi_hd.ico;.',
    '--add-data=exe_icon_256.ico;.',
    '--add-data=exe_icon_128.ico;.',
    '--add-data=exe_icon.ico;.',
    '--add-data=tools/aria2c.exe;tools',
    '--clean',
    '--noconfirm'
])

print("Build terminé ! L'exécutable se trouve dans le dossier 'dist'") 