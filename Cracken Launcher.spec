# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['launcher v2.py'],
    pathex=[],
    binaries=[],
    datas=[('crackenlauncher_logo.png', '.'), ('exe_icon_multi_hd.ico', '.'), ('exe_icon_256.ico', '.'), ('exe_icon_128.ico', '.'), ('exe_icon.ico', '.'), ('tools/aria2c.exe', 'tools')],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='Cracken Launcher',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['exe_icon_multi_hd.ico'],
)
